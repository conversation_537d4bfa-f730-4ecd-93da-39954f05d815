import React, { Suspense, useEffect, useState } from 'react';
import { Flex, Spacer, Stack, Text, useDisclosure, useToast } from '@chakra-ui/react';
import { useMasterObservationResponseList } from '@user/lib/medplum-state';
import { LOINC_ORG, NavigationHelper, ROUTE_ACTIONS, ROUTE_VARIABLES, VITALS } from '@user/lib/constants';
// import { RecordedByType } from '@lib/models/misc';
import { ObservationVitalResponsePayload } from '@user/lib/models/questionnaire-response';
import dayjs from 'dayjs';
import { recordVitalsEvents } from '@user/lib/events-analytics-manager';
import { useNavigate } from 'react-router-dom';

import { MasterVital, PatientVitals } from '@lib/models/patient-vitals';
import { useAnalyticsService, useAuthService, usePublicSettings } from '@lib/state';
import { IModal, MODAL_VARIANTS, Modal } from '../../../../../components/Modal';
import { UpsertVitalsForm } from './UpsertVitalsForm';
import { VitalItemCard } from './VitalSidebarComponents';
import { CardSkeleton, GraphModalButtons, SidebarHelperTooltip } from '../SidebarComponents';
import { SidebarEmptyState } from '../SidebarEmptyState';
import { FHIR_CODE_SYSTEM_URL, deleteIdentifier } from '@lib/constants';

function RespiratoryRateItemList({
  answerList,
  setSelectedVital,
  upsertModal,
  onTrackEditClickEvent,
}: {
  answerList: any;
  setSelectedVital: React.Dispatch<React.SetStateAction<PatientVitals | null>>;
  upsertModal: any;
  onTrackEditClickEvent: () => void;
}) {
  const toast = useToast();
  const { trackEventInFlow } = useAnalyticsService();
  const { authenticatedUser } = useAuthService();
  const { isPublicMode } = usePublicSettings();
  const { deleteObservationResponseTask } = useMasterObservationResponseList(authenticatedUser?.id);

  const onRemoveVital = async (vital: any) => {
    const deleteTask = 'Delete Vitals';
    const identifier = `${deleteIdentifier}:vitals`;
    const payload: ObservationVitalResponsePayload = {
      observationId: vital?.id,
      deleteTask,
      identifier,
    };
    try {
      await deleteObservationResponseTask(payload);

      toast({
        title: `${VITALS.RespiratoryRate.label} removed`,
        // description: 'Respiratory rate has been removed from the list',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (e) {
      toast({
        title: 'Error',
        description: `An error occurred while removing the ${VITALS.RespiratoryRate.label}`,
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      recordVitalsEvents(trackEventInFlow, {
        EventName: 'VTRRRemoved',
      });
    }
  };

  return (
    <>
      {answerList.length === 0 && (
        <Flex
          direction="column"
          gap="12px"
          height="full"
          overflowY="hidden"
        >
          <SidebarEmptyState
            title="No Respiratory Rate recordings found."
            imageSrc="/empty-card-vital-respiratory-rate.png"
            hideActionButton
          />
        </Flex>
      )}
      <Stack
        overflowY="scroll"
        className="hide-scrollbar"
        gap="2"
      >
        {answerList.map((answer: any) => (
          <VitalItemCard
            key={answer.id}
            vitals={answer}
            vitalDetails={
              <Flex alignItems="baseline">
                <Text
                  fontSize="32px"
                  color="gray.500"
                >
                  {answer?.valueQuantity?.value}
                </Text>
                <Text
                  fontSize="sm"
                  color="fluentHealthText.300"
                  ml="2"
                  className="capitalize"
                >
                  {answer?.valueQuantity?.unit || VITALS.RespiratoryRate.unit}
                </Text>
              </Flex>
            }
            bottomLabel={
              answer?.effectiveDateTime &&
              `Recorded at ${dayjs(answer.effectiveDateTime)
                .utcOffset(-5 * 60)
                .utc()
                .format('HH:mm')}`
            }
            onRemove={() => onRemoveVital(answer)}
            onEdit={() => {
              setSelectedVital(answer);
              upsertModal.onOpen();
              onTrackEditClickEvent();
            }}
            isPublicMode={isPublicMode}
          />
        ))}
      </Stack>
    </>
  );
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function RespiratoryRateSection({ masterVital, action }: { masterVital: MasterVital; action: string }) {
  const [modalState, setModalState] = useState<IModal>({});
  const { authenticatedUser } = useAuthService();
  const { isPublicMode } = usePublicSettings();
  const { PROFILE, EHR, VITALS: VITALS_ROUTE, RESPIRATORY_RATE } = ROUTE_VARIABLES;
  const { ADD } = ROUTE_ACTIONS;
  const { trackEventInFlow } = useAnalyticsService();
  const { masterVitalList, addObservationResponse, updateObservationResponse } = useMasterObservationResponseList(
    authenticatedUser?.id
  );
  const answerList = masterVitalList[`vi:respiratory-rate`];

  const [selectedVital, setSelectedVital] = useState<PatientVitals | null>(null);

  // const graphModal = useDisclosure();
  const toast = useToast();
  const navigate = useNavigate();
  const upsertModal = useDisclosure();

  const onAddHandler = () => {
    navigate(`/${PROFILE}/${EHR}/${VITALS_ROUTE}/${RESPIRATORY_RATE}/${ADD}`);
    recordVitalsEvents(trackEventInFlow, {
      EventName: 'VTRRAddStarted',
      vt_rr_entry_point: 'my_health_profile',
    });
  };
  const onTrackEditClickEvent = () => {
    recordVitalsEvents(trackEventInFlow, {
      EventName: 'VTRRInteracted',
      vt_rr_entry_point: 'my_health_profile',
    });
  };

  async function onSubmit(vitalsToUpsert: any) {
    const { id: selectedRespiratoryRateId, dateOfRecording, timeOfRecording } = vitalsToUpsert || {};
    const respiratoryRate = vitalsToUpsert[VITALS.RespiratoryRate.key];
    try {
      const effectiveDateTime =
        dateOfRecording && timeOfRecording
          ? dayjs(`${dateOfRecording}T${timeOfRecording}`).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
          : undefined;

      const payloadData: ObservationVitalResponsePayload = {
        ...(selectedRespiratoryRateId && { id: selectedRespiratoryRateId }),
        item: VITALS.RespiratoryRate.key,
        code: {
          coding: [
            {
              system: FHIR_CODE_SYSTEM_URL,
              code: VITALS.RespiratoryRate.key,
              display: VITALS.RespiratoryRate.label,
            },
            {
              system: LOINC_ORG,
              code: VITALS.RespiratoryRate.code,
              display: VITALS.RespiratoryRate.label,
            },
          ],
        },
        recordingDateTime: effectiveDateTime,
        valueQuantity: {
          value: parseFloat(respiratoryRate),
          unit: VITALS.RespiratoryRate.unit,
          system: 'http://unitsofmeasure.org',
          code: 'breaths/min',
        },
      };

      if (selectedRespiratoryRateId) {
        await updateObservationResponse(payloadData);
      } else {
        await addObservationResponse(payloadData);
      }
      toast({
        title: `Successfully ${selectedRespiratoryRateId ? 'updated' : 'added'} ${VITALS.RespiratoryRate.label}`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      upsertModal.onClose();
      navigate(NavigationHelper.getEhrView(false, VITALS_ROUTE, RESPIRATORY_RATE));
    } catch (err) {
      toast({
        title: 'Something went wrong. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      recordVitalsEvents(trackEventInFlow, {
        EventName: selectedRespiratoryRateId ? 'VTRREdited' : 'VTRRAddCompleted',
        vt_rr_rate: respiratoryRate,
        vt_rr_date: dayjs(dateOfRecording).format('YYYY/MM/DD'),
        vt_rr_time: timeOfRecording,
      });
    }
  }

  // Reset selectedVital when closing the modal
  useEffect(() => {
    if (upsertModal.isOpen) {
      return;
    }
    setSelectedVital(null);
  }, [upsertModal.isOpen]);

  useEffect(() => {
    if (action === ADD) {
      upsertModal.onOpen();
    }
  }, [action]);

  return (
    <>
      <Modal
        title={VITALS.RespiratoryRate.label}
        primaryButtonLabel={selectedVital ? 'Save' : 'Add'}
        showSecondaryButton={false}
        variant={MODAL_VARIANTS.PERIWINKLE}
        isCentered
        maxH="1000px"
        scrollY="false"
        {...modalState}
        {...upsertModal}
        onClose={() => {
          upsertModal.onClose();
          navigate(NavigationHelper.getEhrView(false, VITALS_ROUTE, RESPIRATORY_RATE));
        }}
      >
        <UpsertVitalsForm
          mainFieldName={VITALS.RespiratoryRate.key}
          mainFieldLabel="Breaths per minute*"
          myFormState={selectedVital ? 'Edit' : 'Add'}
          selectedVital={selectedVital}
          onSubmit={onSubmit}
          setModalState={setModalState}
          minReqValue={2}
          maxReqValue={60}
        />
      </Modal>

      {answerList.length === 0 ? (
        <SidebarEmptyState
          title="Update respiratory rate readings"
          actionButtonText="Add"
          imageSrc="/empty-card-vital-respiratory-rate.png"
          {...(isPublicMode ? { hideActionButton: true } : { onClick: onAddHandler })}
          isPublicMode={isPublicMode}
        />
      ) : (
        <Stack
          gap="2"
          height="full"
          width="full"
          overflowY="hidden"
          className="hide-scrollbar"
        >
          <GraphModalButtons {...(isPublicMode ? {} : { onAddClick: onAddHandler })} />
          <Suspense fallback={<CardSkeleton />}>
            <RespiratoryRateItemList
              answerList={answerList}
              setSelectedVital={setSelectedVital}
              upsertModal={upsertModal}
              onTrackEditClickEvent={onTrackEditClickEvent}
            />
          </Suspense>
          <Spacer />
        </Stack>
      )}
      {!isPublicMode && (
        <SidebarHelperTooltip
          text="What is respiratory rate?"
          tooltipText="Johns Hopkins Medicine defines respiratory rate as the number of breaths taken per minute. It can be measured by counting the number of breaths taken in one minute while at rest. The normal respiratory rate for an adult at rest is 12-18 breaths per minute. A respiratory rate under 12 or over 25 breaths per minute at rest may be a sign of an underlying health condition."
        />
      )}
    </>
  );
}
