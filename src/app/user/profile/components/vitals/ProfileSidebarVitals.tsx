import React, { useEffect, useState } from 'react';
import { ChevronLeft as ChevronLeftIcon } from 'react-feather';
import { Card, Container, Flex, Stack, VStack, useTheme } from '@chakra-ui/react';
// import dayjs from 'dayjs';
import { recordVitalsEvents } from '@user/lib/events-analytics-manager';
import { useMasterObservationResponseList } from '@user/lib/medplum-state';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router-dom';
import { useSharePatient } from '@user/lib/state';

import { SidebarMenuItem } from '../SidebarMenuItem';
import { BloodOxygenSection } from './BloodOxygenComponents';
import { BloodPressureSection } from './BloodPressureComponents';
import { ISidebarProps } from '@lib/models/misc';
import { MasterVital } from '@lib/models/patient-vitals';
import { useAnalyticsService, useAuthService, usePublicSettings } from '@lib/state';
import { TemperatureSection } from './TemperatureComponents';
import { PulseSection } from './PulseComponents';
import { RespiratoryRateSection } from './RespiratoryRateComponents';
import { SidebarCloseButton, SidebarHeading } from '../SidebarComponents';
import { NavigationHelper, ROUTE_ACTIONS, ROUTE_VARIABLES, VITALS } from 'src/app/user/lib/constants';
import { toCapitalize } from '@lib/constants';

export const MENU_ITEM_COMPONENTS = {
  [VITALS.BloodPressure.label]: (masterVital: MasterVital, action: string) => (
    <BloodPressureSection
      masterVital={masterVital}
      action={action}
    />
  ),
  [VITALS.BodyTemperature.label]: (masterVital: MasterVital, action: string) => (
    <TemperatureSection
      masterVital={masterVital}
      action={action}
    />
  ),
  [VITALS.OxygenSaturationLevel.label]: (masterVital: MasterVital, action: string) => (
    <BloodOxygenSection
      masterVital={masterVital}
      action={action}
    />
  ),
  [VITALS.PulseRate.label]: (masterVital: MasterVital, action: string) => (
    <PulseSection
      masterVital={masterVital}
      action={action}
    />
  ),
  [VITALS.RespiratoryRate.label]: (masterVital: MasterVital, action: string) => (
    <RespiratoryRateSection
      masterVital={masterVital}
      action={action}
    />
  ),
};

export default function ProfileSidebarVitals({ onClose, action, subActive }: ISidebarProps) {
  const theme = useTheme();
  const navigate = useNavigate();
  const { PROFILE, EHR, VITALS: VITALS_ROUTE } = ROUTE_VARIABLES;
  const { VIEW } = ROUTE_ACTIONS;
  const [currentVital, setCurrentVital] = useState<MasterVital | null>(null);
  const { isPublicMode } = usePublicSettings();

  const { trackEventInFlow } = useAnalyticsService();
  const { authenticatedUser: authenticatedPatient } = useAuthService();

  const patientId = !isPublicMode ? authenticatedPatient.id : useSharePatient()?.patient.id;
  const { masterVitalList } = useMasterObservationResponseList(patientId);
  const showDate = (value: keyof typeof VITALS) => {
    const effectiveDateTime = masterVitalList[VITALS[value].key]?.[0]?.effectiveDateTime;
    return effectiveDateTime ? dayjs(effectiveDateTime).format('DD.MM.YYYY') : '';
  };

  const showValueQuantity = (value: keyof typeof VITALS): string => {
    const { key } = VITALS[value];
    const componentData = masterVitalList[key]?.[0];
    const getUnitByKey = (VitalKey: string) => {
      const vital = Object.values(VITALS).find((v) => v.key === VitalKey);
      return vital ? vital.unit : null;
    };
    if (!componentData) return '';
    if (componentData.component?.length) {
      const [first, second] = componentData.component;
      return `${first?.valueQuantity?.value || ''} / ${second?.valueQuantity?.value || ''} ${
        first?.valueQuantity?.unit || VITALS.BloodPressure.unit
      }`.trim();
    }
    return `${componentData.valueQuantity?.value || ''} ${
      componentData.valueQuantity?.unit || componentData.valueQuantity?.code || getUnitByKey(key)
    }`.trim();
  };

  const handleGoBack = () => {
    if (!currentVital) {
      return;
    }
    setCurrentVital(null);
    if (!isPublicMode) {
      navigate(NavigationHelper.getEhrView(false, 'vitals'));
    }
  };

  const handleSelectVitals = (name: keyof typeof VITALS, value: (typeof VITALS)[keyof typeof VITALS]) => {
    setCurrentVital({ name, value: value.label });
    recordVitalsEvents(trackEventInFlow, {
      EventName: 'VitalInteracted',
      vt_type: name,
      vt_state: showValueQuantity(name) ? 'filled' : 'empty',
    });
    navigate(`/${PROFILE}/${EHR}/${VITALS_ROUTE}/${value.route}/${VIEW}`);
  };

  useEffect(() => {
    if (!subActive) return;
    const category = Object.entries(VITALS).find((l) => {
      return l[1].route === subActive;
    });
    if (!category) return;
    setCurrentVital({ value: category[1].label, name: category[1].label });
  }, [subActive]);

  return (
    <Container
      position="relative"
      height="full"
      overflowY="scroll"
      overflowX="hidden"
      className="hide-scrollbar"
    >
      <Stack
        py="4"
        height="full"
      >
        <Flex justifyContent="space-between">
          <Flex
            gap="8px"
            alignItems="center"
            cursor="pointer"
            onClick={handleGoBack}
          >
            {currentVital && (
              <ChevronLeftIcon
                size={24}
                color={theme.colors.fluentHealthText[100]}
              />
            )}
            <SidebarHeading>
              {currentVital?.value ? toCapitalize(String(currentVital?.value)) : 'Vitals'}
            </SidebarHeading>
          </Flex>
          <SidebarCloseButton onClick={onClose} />
        </Flex>
        {!currentVital ? (
          <Card
            bgColor="periwinkle.100"
            borderRadius="xl"
            border="1px solid"
            borderColor="fluentHealthSecondary.300"
            boxShadow="0px 1px 4px 0px rgba(73, 90, 228, 0.12);"
            width="full"
            mt="6"
          >
            <VStack
              alignItems="left"
              p="2"
              spacing={1}
            >
              {Object.entries(VITALS).map(([key, vital]) => (
                <SidebarMenuItem
                  key={key}
                  title={vital.label}
                  bottomLabel={showValueQuantity(key as keyof typeof VITALS)}
                  rightLabel={showDate(key as keyof typeof VITALS)}
                  onClick={() => handleSelectVitals(key as keyof typeof VITALS, vital)}
                />
              ))}
            </VStack>
          </Card>
        ) : (
          MENU_ITEM_COMPONENTS[currentVital?.value as keyof typeof MENU_ITEM_COMPONENTS](currentVital, action ?? VIEW)
        )}
      </Stack>
    </Container>
  );
}
