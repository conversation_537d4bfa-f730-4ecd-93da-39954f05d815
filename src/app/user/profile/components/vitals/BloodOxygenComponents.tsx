import React, { Suspense, useEffect, useState } from 'react';
import { Flex, Spacer, Stack, Text, useDisclosure, useToast } from '@chakra-ui/react';
import { useMasterObservationResponseList } from '@user/lib/medplum-state';
import { LOINC_ORG, NavigationHelper, ROUTE_ACTIONS, ROUTE_VARIABLES, VITALS } from '@user/lib/constants';
import { ObservationVitalResponsePayload } from '@user/lib/models/questionnaire-response';
import dayjs from 'dayjs';
import { recordVitalsEvents } from '@user/lib/events-analytics-manager';
import { useNavigate } from 'react-router-dom';

import { MasterVital, PatientVitals } from '@lib/models/patient-vitals';
import { useAnalyticsService, useAuthService, usePublicSettings } from '@lib/state';
import { IModal, MODAL_VARIANTS, Modal } from 'src/components/Modal';
import { UpsertVitalsForm } from './UpsertVitalsForm';
import { VitalItemCard } from './VitalSidebarComponents';
import { CardSkeleton, GraphModalButtons, SidebarHelperTooltip } from '../SidebarComponents';
import { SidebarEmptyState } from '../SidebarEmptyState';
import { FHIR_CODE_SYSTEM_URL, deleteIdentifier } from '@lib/constants';

function BloodOxygenItemList({
  answerList,
  onEditVitalsHandler,
  onTrackEditClickEvent,
}: {
  answerList: any;
  onEditVitalsHandler: (vitals: PatientVitals) => void;
  onTrackEditClickEvent: () => void;
}) {
  const toast = useToast();
  const { trackEventInFlow } = useAnalyticsService();
  const { authenticatedUser } = useAuthService();
  const { deleteObservationResponseTask } = useMasterObservationResponseList(authenticatedUser?.id);
  const { isPublicMode } = usePublicSettings();

  const onRemoveVital = async (vital: any) => {
    const deleteTask = 'Delete Vitals';
    const identifier = `${deleteIdentifier}:vitals`;
    const payload: ObservationVitalResponsePayload = {
      observationId: vital?.id,
      deleteTask,
      identifier,
    };
    try {
      await deleteObservationResponseTask(payload);
      toast({
        title: 'Oxygen saturation level removed',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: (error as any).message ?? 'Cannot remove vital!',
        description: 'Something went wrong. Please try again later.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      recordVitalsEvents(trackEventInFlow, {
        EventName: 'VTOSLRemoved',
      });
    }
  };

  return (
    <>
      {answerList?.length === 0 && (
        <Flex
          direction="column"
          gap="12px"
          height="full"
          overflowY="hidden"
        >
          <SidebarEmptyState
            title="No Blood Oxygen recordings found."
            imageSrc="/empty-card-vital-oxygen.png"
            hideActionButton
          />
        </Flex>
      )}
      <Stack
        overflowY="scroll"
        className="hide-scrollbar"
        gap="2"
      >
        {answerList?.map((answer: any) => (
          <VitalItemCard
            key={answer.id}
            vitals={answer}
            vitalDetails={
              <Flex alignItems="baseline">
                <Text
                  fontSize="32px"
                  color="gray.500"
                >
                  {answer?.valueQuantity?.value}
                </Text>
                <Text
                  fontSize="sm"
                  color="fluentHealthText.300"
                  ml="2"
                >
                  {answer?.valueQuantity?.unit || answer?.valueQuantity?.code || VITALS.OxygenSaturationLevel.unit}
                </Text>
              </Flex>
            }
            bottomLabel={
              answer?.effectiveDateTime &&
              `Recorded at ${dayjs(answer.effectiveDateTime)
                .utcOffset(-5 * 60)
                .utc()
                .format('HH:mm')}`
            }
            onRemove={() => onRemoveVital(answer)}
            onEdit={() => {
              onEditVitalsHandler(answer);
              onTrackEditClickEvent();
            }}
            isPublicMode={isPublicMode}
          />
        ))}
      </Stack>
    </>
  );
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function BloodOxygenSection({ masterVital, action }: { masterVital: MasterVital; action: string }) {
  const { authenticatedUser } = useAuthService();
  const { trackEventInFlow } = useAnalyticsService();
  const { isPublicMode } = usePublicSettings();
  const { PROFILE, EHR, VITALS: VITALS_ROUTE, OXYGEN_SATURATION_LEVEL } = ROUTE_VARIABLES;
  const { ADD } = ROUTE_ACTIONS;
  const { masterVitalList, addObservationResponse, updateObservationResponse } = useMasterObservationResponseList(
    authenticatedUser?.id
  );
  const answerList = masterVitalList[VITALS.OxygenSaturationLevel.key];
  const [modalState, setModalState] = useState<IModal>({});
  const [selectedVital, setSelectedVital] = useState<PatientVitals | null>(null);

  const toast = useToast();
  const navigate = useNavigate();
  const upsertModal = useDisclosure();

  const onEditVitalsHandler = async (vitals: PatientVitals) => {
    await setSelectedVital(vitals);
    upsertModal.onOpen();
  };

  const onAddHandler = () => {
    recordVitalsEvents(trackEventInFlow, {
      EventName: 'VTOSLAddStarted',
      vt_bp_entry_point: 'my_health_profile',
    });
    navigate(`/${PROFILE}/${EHR}/${VITALS_ROUTE}/${OXYGEN_SATURATION_LEVEL}/${ADD}`);
  };
  const onTrackEditClickEvent = () => {
    recordVitalsEvents(trackEventInFlow, {
      EventName: 'VTOSLInteracted',
      vt_osl_entry_point: 'my_health_profile',
    });
  };

  async function onSubmit(vitalsToUpsert: any) {
    const { id: selectedOxygenId, dateOfRecording, timeOfRecording } = vitalsToUpsert || {};
    const oxygenSaturationLevel = vitalsToUpsert[VITALS.OxygenSaturationLevel.key];
    try {
      const effectiveDateTime =
        dateOfRecording && timeOfRecording
          ? dayjs(`${dateOfRecording}T${timeOfRecording}`).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
          : undefined;

      const payloadData: ObservationVitalResponsePayload = {
        ...(selectedOxygenId && { id: selectedOxygenId }),
        item: VITALS.OxygenSaturationLevel.key,
        code: {
          coding: [
            {
              system: FHIR_CODE_SYSTEM_URL,
              code: VITALS.OxygenSaturationLevel.key,
              display: VITALS.OxygenSaturationLevel.label,
            },
            {
              system: LOINC_ORG,
              code: VITALS.OxygenSaturationLevel.code,
              display: VITALS.OxygenSaturationLevel.label,
            },
          ],
        },
        recordingDateTime: effectiveDateTime,
        valueQuantity: {
          value: parseFloat(oxygenSaturationLevel),
          unit: VITALS.OxygenSaturationLevel.unit,
          system: 'http://unitsofmeasure.org',
          code: '%',
        },
      };

      if (selectedOxygenId) {
        await updateObservationResponse(payloadData);
      } else {
        await addObservationResponse(payloadData);
      }
      toast({
        title: `Successfully ${selectedOxygenId ? 'updated' : 'added'} ${VITALS.OxygenSaturationLevel.label}`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      upsertModal.onClose();
      if (action === ADD) navigate(NavigationHelper.getEhrView(false, 'vitals', OXYGEN_SATURATION_LEVEL));
    } catch (err) {
      toast({
        title: 'Something went wrong. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      recordVitalsEvents(trackEventInFlow, {
        EventName: selectedOxygenId ? 'VTOSLEdited' : 'VTOSLAddCompleted',
        vt_osl_level: oxygenSaturationLevel,
        vt_osl_date: dayjs(dateOfRecording).format('YYYY/MM/DD'),
        vt_osl_time: timeOfRecording,
      });
    }
  }

  // Reset selectedVital when closing the modal
  useEffect(() => {
    if (upsertModal.isOpen) {
      return;
    }
    setSelectedVital(null);
  }, [upsertModal.isOpen]);

  useEffect(() => {
    if (action === ADD) {
      upsertModal.onOpen();
    }
  }, [action]);

  const closeFn = () => {
    upsertModal.onClose();
    if (action === ADD) navigate(NavigationHelper.getEhrView(false, 'vitals', OXYGEN_SATURATION_LEVEL));
  };

  return (
    <>
      <Modal
        title={VITALS.OxygenSaturationLevel.label}
        primaryButtonLabel={selectedVital ? 'Save' : 'Add'}
        showSecondaryButton={false}
        isCentered
        maxH="1000px"
        scrollY="false"
        variant={MODAL_VARIANTS.PERIWINKLE}
        {...modalState}
        {...upsertModal}
        onClose={closeFn}
      >
        <UpsertVitalsForm
          mainFieldName={VITALS.OxygenSaturationLevel.key}
          mainFieldLabel="Oxygen saturation level (%) *"
          myFormState={selectedVital ? 'Edit' : 'Add'}
          selectedVital={selectedVital}
          onSubmit={onSubmit}
          setModalState={setModalState}
          minReqValue={30}
          maxReqValue={100}
        />
      </Modal>

      {answerList.length === 0 ? (
        <SidebarEmptyState
          title="Update oxygen saturation level readings"
          actionButtonText="Add"
          imageSrc="/empty-card-vital-oxygen.png"
          {...(isPublicMode ? { hideActionButton: true } : { onClick: onAddHandler })}
          isPublicMode={isPublicMode}
        />
      ) : (
        <Stack
          gap="2"
          height="full"
          width="full"
          overflowY="hidden"
          sx={{
            '::-webkit-scrollbar': {
              display: 'none',
              msOverflowStyle: 'none',
              scrollbarWidth: 'none',
            },
          }}
        >
          <GraphModalButtons {...(isPublicMode ? {} : { onAddClick: onAddHandler })} />
          <Suspense fallback={<CardSkeleton />}>
            <BloodOxygenItemList
              answerList={answerList}
              onEditVitalsHandler={onEditVitalsHandler}
              onTrackEditClickEvent={onTrackEditClickEvent}
            />
          </Suspense>
          <Spacer />
        </Stack>
      )}
      {!isPublicMode && (
        <SidebarHelperTooltip
          text="What is oxygen saturation level?"
          tooltipText="The WHO defines oxygen saturation levels as a measurement of haemoglobin carrying oxygen in the blood compared to haemoglobin not carrying oxygen. Oxygen is essential for the treatment of hypoxaemia (low blood oxygen saturation) and should be given to the patient to improve and stabilise blood oxygen saturation levels."
        />
      )}
    </>
  );
}
