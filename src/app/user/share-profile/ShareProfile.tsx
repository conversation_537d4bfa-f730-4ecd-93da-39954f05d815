import { Suspense, lazy, useEffect, useRef, useState } from 'react';
import { Box, Container, Grid, GridItem } from '@chakra-ui/react';
import { useIsTablet } from '@components/ui/hooks/device.hook';
import { useSharePatient } from '@user/lib/state';

import ProfileCard from './components/ProfileCard';
import CardWrapper from './components/CardWrapper';

// Lazy load heavy components
const HealthSnapshot = lazy(() => import('./components/HealthSnapshot'));
const HealthTimeline = lazy(() => import('./components/HealthTimeline'));
const ProfileTabs = lazy(() => import('./components/ProfileTabs'));

// Loading fallback component
function LoadingFallback() {
  return (
    <Box
      p={4}
      textAlign="center"
    >
      <div>Loading...</div>
    </Box>
  );
}

function MobileShareProfilePage({ patient }: { patient: any }) {
  return (
    <Box>
      <ProfileCard patient={patient} />
      <Suspense fallback={<LoadingFallback />}>
        <ProfileTabs />
      </Suspense>
    </Box>
  );
}

function useScrollDirection(throttleMs: number) {
  const [scrollUp, setScrollUp] = useState(false);
  const lastScrollY = useRef(0);
  const lastCallTime = useRef<number>(0);

  useEffect(() => {
    const handleScroll = () => {
      const now = Date.now();

      // Throttle: only process if enough time has passed
      if (now - lastCallTime.current < throttleMs) {
        return;
      }

      lastCallTime.current = now;
      const currentScrollY = window.scrollY;
      const isScrollUp = currentScrollY > lastScrollY.current;

      // Only update state when the direction changes and there's significant movement
      if (Math.abs(currentScrollY - lastScrollY.current) > 5) {
        setScrollUp(isScrollUp);
        lastScrollY.current = currentScrollY;
      }
    };

    // Use scroll event instead of wheel to track actual page scroll
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [throttleMs]);

  return scrollUp;
}

function ShareProfilePage() {
  const isTablet = useIsTablet();
  const scrollUp = useScrollDirection(100);

  const { patient } = useSharePatient();

  if (isTablet) {
    return <MobileShareProfilePage patient={patient} />;
  }
  return (
    <Box>
      <Container
        maxW="1200px"
        centerContent
        transition="1.3s all ease-in-out"
      >
        <ProfileCard
          scrollUp={scrollUp}
          patient={patient}
        />
        <Grid
          templateColumns={{ base: '1fr', lg: 'repeat(3, 1fr)' }}
          gap={4}
          w="full"
        >
          <GridItem>
            <CardWrapper
              title="Snapshot"
              scrollUp={scrollUp}
            >
              <Suspense fallback={<LoadingFallback />}>
                <HealthSnapshot />
              </Suspense>
            </CardWrapper>
          </GridItem>
          <GridItem>
            <CardWrapper
              title="Timeline"
              scrollUp={scrollUp}
            >
              <Suspense fallback={<LoadingFallback />}>
                <HealthTimeline />
              </Suspense>
            </CardWrapper>
          </GridItem>
          <GridItem>
            <Suspense fallback={<LoadingFallback />}>
              <ProfileTabs scrollUp={scrollUp} />
            </Suspense>
          </GridItem>
        </Grid>
      </Container>
    </Box>
  );
}

export default ShareProfilePage;
