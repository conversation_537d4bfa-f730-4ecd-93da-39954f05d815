import React from 'react';
import { Box, Text } from '@chakra-ui/react';

interface CardWrapperProps {
  title: string;
  children: React.ReactNode;
  scrollUp: boolean;
}

function CardWrapper({ title, children, scrollUp }: CardWrapperProps) {
  return (
    <Box
      borderRadius="16px 20px"
      borderTop="2px solid "
      borderColor="iris.500"
      boxShadow="md"
      mt="16px"
      p={4}
      textAlign="center"
      maxW="md"
      bgColor="periwinkle.100"
      mx="auto"
      maxHeight={scrollUp ? '63vh' : '45vh'}
      minHeight={scrollUp ? '63vh' : '45vh'}
      display="flex"
      flexDirection="column"
    >
      <Text
        fontSize="16px"
        fontWeight="500"
        color="gray.500"
        mb="13px"
        flexShrink={0}
      >
        {title}
      </Text>

      <Box
        flex={1}
        overflowY="auto"
      >
        {children}
      </Box>
    </Box>
  );
}

export default CardWrapper;
