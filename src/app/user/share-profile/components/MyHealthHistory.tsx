import React, { Suspense, useEffect, useState } from 'react';
import {
  <PERSON>,
  CardBody,
  Divider,
  Drawer,
  DrawerContent,
  DrawerOverlay,
  Flex,
  Heading,
  Spacer,
  Text,
  VStack,
  useDisclosure,
  useTheme,
} from '@chakra-ui/react';
import { MY_HEALTH_PROFILE_MAP_SHARE } from '@user/lib/constants';
import { useParams, useSearchParams } from 'react-router-dom';
import { useShareFamilyMemberHistoryList } from '@user/lib/medplum-state';
import { ChevronRightIcon } from '@chakra-ui/icons';
import { hexOpacity } from '@components/theme/utils';
import { getAge } from '@utils/utils';
import { FluentHealthLoader } from '@components/FluentHealthLoader';
import { medplumApi } from '@user/lib/medplum-api';

import { SidebarConditions } from '../../profile/family-history/components/SidebarConditions';
import { FACT_CODE_SYSTEM } from '@lib/constants';
import { DrawerSection } from '../../profile/components/ProfileSidebarCTAs';
import EmptyState from './EmptyState';

function FamilyMemberCard({ member }: { member: any }) {
  const theme = useTheme();
  const [bloodGroup, setBloodGroup] = useState<any[]>([]);
  const [ethnicityGrp, setEthnicityGrp] = useState<any[]>([]);
  const [searchParams] = useSearchParams();

  useEffect(() => {
    Promise.all([
      medplumApi.valueSetList.getAllValueSetFromShareDirectus(
        true,
        FACT_CODE_SYSTEM.ETHNICITY,
        searchParams.get('access_token')
      ),
      medplumApi.valueSetList.getAllValueSetFromShareDirectus(
        true,
        FACT_CODE_SYSTEM.BLOOD_GROUP,
        searchParams.get('access_token')
      ),
    ]).then(([inEthnicity, bloodGroupData]) => {
      setEthnicityGrp(inEthnicity.map((e: { display: string; code: string }) => ({ label: e.display, value: e.code })));
      setBloodGroup(
        bloodGroupData.map((e: { display: string; code: string }) => ({ label: e.display, value: e.code }))
      );
    });
  }, []);

  const ethnicityURL =
    member?.extension && member?.extension.find((extension: any) => extension.url.includes('FACT-eth'));
  const bloodGroupURL =
    member?.extension && member?.extension.find((extension: any) => extension.url.includes('FACT-Category-bg'));
  const bloodType = bloodGroup.find((item) => item.value === bloodGroupURL?.valueCode);
  const ethnicity = ethnicityGrp.find((item) => item.value === ethnicityURL?.valueCode);
  const conditionSidebar = useDisclosure();

  const openConditionsSidebar = () => {
    conditionSidebar.onOpen();
  };

  return (
    <>
      <Drawer
        isOpen={conditionSidebar.isOpen}
        placement="right"
        onClose={conditionSidebar.onClose}
        size="sm"
        blockScrollOnMount={false}
      >
        <DrawerOverlay />
        <DrawerContent bg="gradient.profileDrawer">
          <Suspense
            fallback={
              <FluentHealthLoader
                position="absolute"
                top={0}
                bottom={0}
                left={0}
                right={0}
                my="auto"
              />
            }
          >
            <SidebarConditions
              relatedPerson={member}
              onClose={conditionSidebar.onClose}
            />
          </Suspense>
        </DrawerContent>
      </Drawer>
      <Flex
        gap="16px"
        direction="column"
      >
        <Card
          bgColor="fluentHealthSecondary.500"
          w="full"
          boxShadow="none"
          borderRadius="8px"
          role="group"
        >
          <CardBody
            padding="12px 8px 12px 12px"
            display="flex"
            flexDirection="column"
            justifyContent="space-between"
          >
            <Flex
              direction="column"
              px="2"
              gap="1"
            >
              <Heading
                fontSize="20px"
                color="fluentHealthSecondary.100"
                lineHeight="8"
              >
                {member?.name || ''}
              </Heading>
              <Heading
                fontSize="16px"
                color="fluentHealthSecondary.200"
                mt="4px"
              >
                {member?.relationship?.coding[0]?.display || ''}
              </Heading>
            </Flex>
          </CardBody>
        </Card>
        <Flex
          direction="column"
          flex={1}
          gap="16px"
        >
          <Flex
            justifyContent="space-between"
            direction="column"
            gap="8px"
            paddingBottom="2"
            borderBottom="1px solid"
            borderColor="gray.100"
            mx="2"
          >
            <Text color="gray.300">Age</Text>
            <Text color="fluentHealthText.100">
              {member?.bornDate && !Number.isNaN(getAge(member?.bornDate)) ? getAge(member?.bornDate) : '—'}
            </Text>
          </Flex>
          <Flex
            justifyContent="space-between"
            direction="column"
            gap="8px"
            paddingBottom="2"
            borderBottom="1px solid"
            borderColor="gray.100"
            mx="2"
          >
            <Text color="gray.300">Status</Text>
            <Text color="fluentHealthText.100">
              {member?.deceasedBoolean === true ? 'Deceased' : member?.deceasedBoolean === false ? 'Living' : '—'}
            </Text>
          </Flex>
          <Flex
            justifyContent="space-between"
            direction="column"
            mx="2"
            pb="2"
            paddingBottom="2"
            borderBottom="1px solid"
            borderColor="gray.100"
          >
            <Text color="gray.300">Blood group</Text>
            <Text color="fluentHealthText.100">{bloodType?.label || '—'}</Text>
          </Flex>
          <Flex
            justifyContent="space-between"
            direction="column"
            mx="2"
            pb="2"
          >
            <Text color="gray.300">Ethnicity</Text>
            <Text color="fluentHealthText.100">{ethnicity?.label || '—'}</Text>
          </Flex>
          <Flex justifyContent="space-between">
            <Flex
              alignItems="center"
              direction="column"
              w="100%"
            >
              <Card
                bgColor="transparent"
                borderRadius="xl"
                border="1px solid"
                borderColor="periwinkle.400"
                boxShadow={`0px 1px 4px ${hexOpacity(theme.colors.royalBlue['500'], 0.12)}`}
                w="100%"
              >
                <Flex
                  direction="column"
                  p="2"
                  gap="1"
                >
                  <Flex
                    h="12"
                    alignItems="center"
                    px="2"
                    fontSize="lg"
                    fontWeight="medium"
                    lineHeight="short"
                    _hover={{ cursor: 'pointer', bgColor: 'periwinkle.200', borderRadius: 'lg' }}
                    onClick={() => openConditionsSidebar()}
                  >
                    Conditions
                    <Spacer />
                    <ChevronRightIcon
                      fontSize="xl"
                      color="papaya.600"
                    />
                  </Flex>
                </Flex>
              </Card>
            </Flex>
          </Flex>
        </Flex>
      </Flex>
    </>
  );
}

export default function MyHealthHistory() {
  const params = useParams();
  const { ehrId, subEhrId = '', action } = params as any;
  const sharePatientId = localStorage.getItem('sharePatientId');
  const familyMemberList = useShareFamilyMemberHistoryList(sharePatientId);

  return (
    <Flex direction="column">
      <Text
        fontSize="13"
        textTransform="uppercase"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
        mb="8px"
      >
        My Health History
      </Text>
      <Text
        textAlign="left"
        color="charcoal.60"
        fontSize="sm"
        letterSpacing="-.28px"
        pb="24px"
        pt="8px"
      >
        A detailed record of your conditions, procedures, and medications—the single source of truth for your health.
      </Text>
      <Card
        bgColor="transparent"
        borderRadius="xl"
        boxShadow="0px"
        w="full"
      >
        <VStack alignItems="left">
          {Object.entries(MY_HEALTH_PROFILE_MAP_SHARE).map(([key, value]) => (
            <DrawerSection
              key={key}
              name={value.name}
              active={ehrId}
              subEhrId={subEhrId || ''}
              action={action}
              // route={value.route}
            />
          ))}
        </VStack>
      </Card>
      <Text
        fontSize="13"
        textTransform="uppercase"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
        mt="20px"
      >
        My FAMILY history
      </Text>
      <Text
        textAlign="left"
        color="charcoal.60"
        fontSize="sm"
        letterSpacing="-.28px"
        pt="8px"
      >
        A snapshot of the health you&apos;ve inherited. Track relevant details from your immediate family to help map
        potential risks and proactively plan for your future care.
      </Text>
      {familyMemberList && familyMemberList.length > 0 ? (
        <Flex
          direction="column"
          gap={{ base: '6', md: '5' }}
        >
          {familyMemberList &&
            familyMemberList.map((member: any, index: number) => {
              return (
                <React.Fragment key={member?.id}>
                  <FamilyMemberCard member={member} />
                  {index !== familyMemberList.length - 1 && <Divider borderColor="gray.100" />}
                </React.Fragment>
              );
            })}
        </Flex>
      ) : (
        <EmptyState
          message="No family members added"
          margin="16px 0px 0px 0px"
        />
      )}
    </Flex>
  );
}
