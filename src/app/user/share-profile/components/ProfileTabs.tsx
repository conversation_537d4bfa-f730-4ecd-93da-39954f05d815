import { useState } from 'react';
import { Box, Flex } from '@chakra-ui/react';
import { useIsTablet } from '@components/ui/hooks/device.hook';

import { TabLink } from 'src/components/ui/Tab';
import MyHealthHistory from './MyHealthHistory';
import BasicInfo from './BasicInfo';
import HealthSnapshot from './HealthSnapshot';
import HealthTimeline from './HealthTimeline';

function MobileProfileTabs() {
  const [selectedTab, setSelectedTab] = useState(0);
  return (
    <Flex
      direction="column"
      w="100%"
      mt="16px"
    >
      <Flex
        px="20px"
        gap="8px"
        overflowX="auto"
        className="hide-scrollbar"
      >
        <TabLink
          to=""
          onClick={() => {
            setSelectedTab(0);
          }}
          isActive={selectedTab === 0}
        >
          Snapshot
        </TabLink>
        <TabLink
          to=""
          onClick={() => {
            setSelectedTab(1);
          }}
          isActive={selectedTab === 1}
        >
          Timeline
        </TabLink>
        <TabLink
          to=""
          onClick={() => {
            setSelectedTab(2);
          }}
          isActive={selectedTab === 2}
        >
          Health History
        </TabLink>
        <TabLink
          to=""
          onClick={() => {
            setSelectedTab(3);
          }}
          isActive={selectedTab === 3}
        >
          Basic Info
        </TabLink>
      </Flex>
      <Box
        p="20px"
        bgColor="periwinkle.100"
      >
        {selectedTab === 0 && (
          <Flex
            direction="column"
            gap={4}
          >
            <HealthSnapshot />
          </Flex>
        )}
        {selectedTab === 1 && (
          <Flex
            direction="column"
            gap={4}
          >
            <HealthTimeline />
          </Flex>
        )}
        {selectedTab === 2 && <MyHealthHistory />}
        {selectedTab === 3 && <BasicInfo />}
      </Box>
    </Flex>
  );
}

export default function ProfileTabs({ scrollUp }: { scrollUp?: boolean }) {
  const [selectedTab, setSelectedTab] = useState(0);
  const isTablet = useIsTablet();

  if (isTablet) {
    return <MobileProfileTabs />;
  }
  return (
    <Flex
      direction="column"
      w="100%"
      mt="16px"
      display="flex"
      flexDirection="column"
      borderRadius="20px"
    >
      <Flex
        px="10px"
        gap="8px"
      >
        <TabLink
          to=""
          onClick={() => {
            setSelectedTab(0);
          }}
          isActive={selectedTab === 0}
        >
          Health History
        </TabLink>
        <TabLink
          to=""
          onClick={() => {
            setSelectedTab(1);
          }}
          isActive={selectedTab === 1}
        >
          Basic Info
        </TabLink>
      </Flex>
      <Box
        p="15px"
        bgColor="periwinkle.100"
        borderRadius="10px 10px 20px 20px"
        boxShadow="md"
      >
        <Box
          overflowY="auto"
          maxHeight={scrollUp ? 'calc(63vh - 78px)' : 'calc(45vh - 78px)'}
          minHeight={scrollUp ? 'calc(63vh - 78px)' : 'calc(45vh - 78px)'}
        >
          {selectedTab === 0 && <MyHealthHistory />}
          {selectedTab === 1 && <BasicInfo />}
        </Box>
      </Box>
    </Flex>
  );
}
