import { Box, Flex, Image, Text } from '@chakra-ui/react';
import { useIsTablet } from '@components/ui/hooks/device.hook';

import { ProfileBioCard } from './ProfileBioCard';
import { parsePatientName } from '@lib/utils/utils';

import ProfileIcon from '@assets/icons/profile-icon.svg';

function MobileProfilePage(props: any) {
  return (
    <Box
      position="relative"
      bgColor="fluentHealth.500"
      bgImage="/mobile-background-profile-cover.png"
      bgPos="center"
      margin="5px 16px"
      borderRadius="4xl"
      h="220px"
    >
      {/* Left: Icon and Name */}
      <Flex
        flexDir="column"
        align="center"
        justify="center"
        width="100%"
        height="100%"
        position="absolute"
        top="0"
        left="0"
      >
        <Image
          src={ProfileIcon}
          width="92px"
          height="92px"
          mb="15px"
        />
        <Text
          fontSize={{ base: 'lg', md: '2xl' }}
          color="white"
        >
          {parsePatientName(props?.patient?.name) || '--'}
        </Text>
      </Flex>
    </Box>
  );
}

function ProfileCard({ patient, scrollUp }: { patient: any; scrollUp?: boolean }) {
  const isTablet = useIsTablet();

  if (isTablet) {
    return <MobileProfilePage patient={patient} />;
  }
  return (
    <Box
      bg="fluentHealth.500"
      color="white"
      borderRadius="16px"
      marginTop="16px"
      w="100%"
    >
      <Flex>
        {/* Left: Icon and Name */}
        <Flex
          flexDir="column"
          align="center"
          justify="center"
          width="30%"
          borderRight={{ md: '1px solid white' }}
        >
          {!scrollUp && (
            <Image
              src={ProfileIcon}
              width="92px"
              height="92px"
              mb="15px"
            />
          )}
          <Text fontSize={{ base: 'lg', md: '2xl' }}>{parsePatientName(patient?.name) || '--'}</Text>
        </Flex>

        {/* Right: Grid Details */}
        <ProfileBioCard
          scrollUp={scrollUp}
          patient={patient}
        />
      </Flex>
    </Box>
  );
}

export default ProfileCard;
