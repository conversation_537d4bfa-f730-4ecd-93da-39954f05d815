import { useState } from 'react';
import { Button, Flex, Stack, Text, useDisclosure, useToast } from '@chakra-ui/react';
// import { NavLink } from 'react-router-dom';
import { recordSettingsEvents } from '@user/lib/events-analytics-manager';
import { useNavigate } from 'react-router-dom';
import { ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';

import { GenericSettingsPage } from './GenericSettingsPage';
import { useDeleteProfileState } from '../lib/medplum-state';
import { useAnalyticsService, useAuthService } from '@lib/state';
import {
  ConsentModal,
  ConsentModalFooter,
  ConsentModalHeading,
  ConsentModalPrimaryButton,
  ConsentModalSecondaryButton,
} from '../profile/components/ConsentModal';

export function DeletePersonalDataAccountPage() {
  const [isDeleting, setIsDeleting] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const { authenticatedUser, logout } = useAuthService();
  const patientId = authenticatedUser?.id;
  const toast = useToast();
  const deleteProfileModal = useDisclosure();
  const { deleteProfileTaskCreate } = useDeleteProfileState();
  const { trackEventInFlow } = useAnalyticsService();
  const navigate = useNavigate();
  const { DASHBOARD } = ROUTE_VARIABLES;
  const { VIEW } = ROUTE_ACTIONS;

  const handleDelete = async () => {
    setLoading(true);
    const payload: any = {
      type: 'transaction',
      resourceType: 'Bundle',
      fullUrl: 'urn:uuid:57f054a5-f6d9-4432-bd27-2ba988cb79d1',
      entry: [
        {
          request: {
            url: 'Task',
            method: 'POST',
          },
          resource: {
            for: {
              reference: `Patient/${patientId}`,
            },
            resourceType: 'Task',
            status: 'requested',
            intent: 'option',
            priority: 'routine',
            code: {
              coding: [
                {
                  code: 'delete-data',
                  display: 'Delete Data',
                },
              ],
            },
            description: 'Deleting Patient Data',
            input: [
              {
                type: {
                  coding: [
                    {
                      code: 'delete-patient-data',
                      display: 'Delete Patient Data',
                    },
                  ],
                },
                valueReference: {
                  reference: `Patient/${patientId}`,
                },
              },
            ],
            identifier: [
              {
                value: 'urn:fh-workflow:task:delete:patient-data',
              },
            ],
          },
        },
      ],
    };
    try {
      const { status } = await deleteProfileTaskCreate(payload);
      if (status === 200) {
        setIsDeleting(true);
        recordSettingsEvents(trackEventInFlow, { EventName: 'AccountDeleted' });
      } else {
        toast({
          title: 'Something went wrong! Try again later.',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
      }
    } catch (err) {
      toast({
        title: (err as any).message,
        status: 'error',
        duration: 4000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
      deleteProfileModal.onClose?.();
      await logout();
      toast({
        title: 'Account Deleted!',
        status: 'info',
        duration: 10000,
        isClosable: true,
      });
      navigate(`/${DASHBOARD}/${VIEW}`);
    }
  };

  const openDeleteModal = async () => {
    deleteProfileModal.onOpen();
    await recordSettingsEvents(trackEventInFlow, {
      EventName: 'AccountDeleteInteracted',
    });
  };

  return (
    <GenericSettingsPage
      title="Delete Personal Data and Account"
      width="100%"
      maxWidth="800px"
    >
      <Stack
        padding="20px"
        spacing="16px"
      >
        <Flex
          flexDirection="column"
          alignItems="flex-start"
          justifyContent="flex-start"
          gap={5}
        >
          <ConsentModal
            {...deleteProfileModal}
            padding="80px"
          >
            <ConsentModalHeading>Are you sure you want to delete your personal data and account?</ConsentModalHeading>
            {/* <ConsentModalContent>This cannot be undone.</ConsentModalContent> */}
            <ConsentModalFooter>
              <ConsentModalPrimaryButton onClick={deleteProfileModal.onClose}>No, go back</ConsentModalPrimaryButton>
              <ConsentModalSecondaryButton
                variant="quietDanger"
                color="red.100"
                isLoading={isLoading}
                onClick={handleDelete}
              >
                Delete account
              </ConsentModalSecondaryButton>
            </ConsentModalFooter>
          </ConsentModal>
          <Text
            fontSize="lg"
            color="fluentHealthText.100"
          >
            {isDeleting ? `We've received your request to delete your account` : 'Request Account Deletion'}
          </Text>
          {isDeleting ? (
            <Text
              fontSize="md"
              color="fluentHealthText.200"
              paddingTop="12px"
            >
              We&apos;re sorry to see you leave. Your account will be deleted in 72 hours. If you change your mind in
              the meantime, please reach out to{' '}
              <a
                href="mailto:<EMAIL>"
                style={{
                  transition: 'all .3s ease',
                  color: '#495AE4',
                }}
              >
                Customer Support
              </a>
              .
            </Text>
          ) : (
            <Text
              fontSize="md"
              color="fluentHealthText.200"
            >
              If you decide to delete your account, please note that it will remove all records of your consultations
              with doctors. Additionally, after your account is deleted, Fluent will no longer be able to provide you
              with services.
            </Text>
          )}

          {!isDeleting && (
            <Button
              bg="fluentHealth.500"
              color="white"
              variant="quietPrimary"
              height="auto"
              padding="8px 16px"
              isLoading={isLoading}
              // _hover={{
              //   bg: 'white',
              //   color: 'fluentHealth.500',
              // }}

              onClick={openDeleteModal}
            >
              Request deletion
            </Button>
          )}
        </Flex>
      </Stack>
    </GenericSettingsPage>
  );
}
