import axios from 'axios';
import { <PERSON>uffer } from 'buffer';

import { FH_UI_CODESYSTEM } from 'src/app/medical-records/lib/constants';
import { ObservationVitalResponsePayload } from './models/questionnaire-response';
import { Patient } from 'src/gql/graphql';
import ApiError from '../../../components/error/ApiError';
import { AuthService } from '@lib/authService';
import { MEDPLUM_API_URL, MEDPLUM_GRAPHQL_API_URL } from '@lib/constants';
import { medplumGraphQlQuery } from './medplum-graphql-query';
import { FH_CODE_SYSTEM_FACT } from 'src/constants/medplumConstants';
import { IdentifyProviderNames, identifyHealthProfileUser } from '@lib/identifyService';

const DELETE_QUESTIONNAIRE_TASK = 'urn:uuid:6aecda40-4b5b-4778-975e-9b6db4ae6bc8 ';

const masterVitalObservation = {
  async getVitalObservationResponseList(url: any, patientId: any) {
    const {
      data: { data },
      status,
    } = await axios.post(
      MEDPLUM_GRAPHQL_API_URL,
      {
        query: medplumGraphQlQuery.masterObservationList.getAll(url, patientId),
      },
      {
        headers: AuthService.instance.withAuthHeader(),
      }
    );

    identifyHealthProfileUser(IdentifyProviderNames.Vitals, data?.ObservationList);
    if (status !== 200) {
      throw new ApiError(data);
    }
    return data.ObservationList;
  },
  async addVitalObservation(patientId: Patient['id'], payloadData: ObservationVitalResponsePayload) {
    const payload = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry: [
        {
          resource: {
            resourceType: 'Observation',
            identifier: [
              {
                system: FH_CODE_SYSTEM_FACT,
                value: payloadData.item,
              },
            ],
            status: 'final',
            code: payloadData.code,
            subject: {
              reference: `Patient/${patientId}`,
            },
            effectiveDateTime: payloadData.recordingDateTime,
            ...(payloadData.method && { method: payloadData.method }),
            ...(payloadData.component && { component: payloadData.component }),
            ...(payloadData.valueQuantity && { valueQuantity: payloadData.valueQuantity }),
          },
          request: {
            method: 'POST',
            url: 'Observation',
          },
        },
      ],
    };

    const response = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, payload, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (response?.status !== 200) {
      throw new Error('Failed to add.');
    }

    return response;
  },
  async updateVitalObservation(patientId: Patient['id'], payloadData: ObservationVitalResponsePayload) {
    const observationResource = {
      resourceType: 'Observation',
      id: payloadData.id,
      identifier: [
        {
          system: FH_CODE_SYSTEM_FACT,
          value: payloadData.item,
        },
      ],
      status: 'final',
      code: payloadData.code,
      subject: { reference: `Patient/${patientId}` },
      effectiveDateTime: payloadData.recordingDateTime,
      ...(payloadData.method && { method: payloadData.method }),
      ...(payloadData.component && { component: payloadData.component }),
      ...(payloadData.valueQuantity && { valueQuantity: payloadData.valueQuantity }),
    };

    const bundlePayload = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry: [
        {
          resource: observationResource,
          request: { method: 'PUT', url: `Observation/${payloadData.id}` },
        },
      ],
    };

    try {
      const response = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, bundlePayload, {
        headers: AuthService.instance.withAuthHeader(),
      });
      console.log(response.data);
    } catch (error) {
      console.error('Error updating the observation:', error);
      throw new Error('Failed to update observation.');
    }
  },
  async deleteVitalObservationTask(payload: any) {
    const { observationId } = payload || {};
    const observationUpdate = Buffer.from(
      JSON.stringify([
        {
          op: 'add',
          path: '/meta/tag',
          value: [
            {
              system: FH_UI_CODESYSTEM,
              code: 'delete',
              display: 'Marked for deletion',
            },
          ],
        },
      ])
    ).toString('base64');
    const batchRequest = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry: [
        {
          fullUrl: DELETE_QUESTIONNAIRE_TASK,
          resource: {
            resourceType: 'Task',
            status: 'requested',
            intent: 'option',
            priority: 'routine',
            identifier: [
              {
                system: FH_CODE_SYSTEM_FACT,
                value: payload.identifier || `delete-task-${observationId}`,
              },
            ],
            input: [
              {
                type: {
                  coding: [
                    {
                      system: FH_CODE_SYSTEM_FACT,
                      code: payload.identifier || `delete-observation-${observationId}`,
                      display: 'Delete Observation Task',
                    },
                  ],
                },
                valueReference: {
                  reference: `Observation/${observationId}`,
                },
              },
            ],
          },
          request: {
            method: 'POST',
            url: 'Task',
          },
        },
        {
          resource: {
            resourceType: 'Binary',
            contentType: 'application/json-patch+json',
            data: `{{${observationUpdate}}}`,
          },
          request: {
            method: 'PATCH',
            url: `Observation/${observationId}`,
          },
        },
      ],
    };
    const response = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, batchRequest, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (response?.status !== 200) {
      throw new Error('Failed to delete observation.');
    }
    return { success: response?.status === 200 };
  },
};
export const medplumObservationApi = {
  masterVitalObservation,
};
