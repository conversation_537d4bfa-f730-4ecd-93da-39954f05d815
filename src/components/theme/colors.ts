// This is a new color palette. These colors should be used instead of the colors below.
const newColor = {
  royalBlue: {
    50: '#f4f3fd',
    100: '#e9e6fc',
    200: '#d2cdf9',
    300: '#a29df1',
    400: '#6c70e9',
    500: '#495ae4',
    600: '#414dc0',
    700: '#39409d',
    800: '#27295c',
    900: '#141222',
  },
  blueViolet: {
    50: '#f7f1ff',
    100: '#f0e3ff',
    200: '#e0c8ff',
    300: '#cfadff',
    400: '#a977ff',
    500: '#7b3efd',
    600: '#592fae',
    700: '#482889',
    800: '#281843',
    900: '#191024',
  },
  seaGreen: {
    50: '#f1f7f5',
    100: '#e2f0ea',
    200: '#c6e1d5',
    300: '#8dc3ad',
    400: '#50a586',
    500: '#289673',
    600: '#236951',
    700: '#205441',
    800: '#162c23',
    900: '#0f1a15',
  },
  salmon: {
    50: '#fff5ef',
    100: '#ffeae0',
    200: '#ffd5c2',
    300: '#ffbfa5',
    400: '#ff936b',
    500: '#ff6333',
    600: '#d7552d',
    700: '#b04827',
    800: '#672e1b',
    900: '#462115',
  },
  sandyBrown: {
    50: '#fff4e4',
    100: '#ffe9ca',
    200: '#ffdeaf',
    300: '#ffd495',
    400: '#ffc97b',
    500: '#ffb442',
    600: '#d7983a',
    700: '#8a632a',
    800: '#664a22',
    900: '#45321a',
  },
  gray: {
    50: '#f7f7f7',
    100: '#e6e8e8',
    200: '#a9aeaf',
    300: '#888d8f',
    400: '#686a6c',
    500: '#14181a',
    600: '#000000',
    700: '#858C8F',
  },
  mintGreen: {
    50: '#E5F9F4',
    100: '#BEF0E4',
    400: '#27CDAF',
  },
  periwinkle: {
    50: '#F8F8FF',
    100: '#F0F1FF',
    200: '#e9eaff',
    300: '#e1e3ff',
    400: '#dadcff',
    600: '#2C3EC4',
    700: '#41399d',
  },
  iris: {
    10: '#EDEEFC',
    100: '#DBDEFA',
    200: '#B6BDF4',
    300: '#929CEF',
    400: '#6D7BE9',
    500: '#495AE4',
    600: '#3F51E3',
  },
  papaya: {
    10: '#FFEFEB',
    20: '#FFEFEA',
    100: '#FFE0D6',
    200: '#FFC1AD',
    300: '#FFA185',
    400: '#FF825C',
    500: '#FF824D',
    600: '#FF6333',
  },
  beige: {
    100: '#FFF7EC',
    200: '#FFF2DF',
  },
  gradient: {
    loginPage: 'linear-gradient(180.96deg, #FFF4E4 -0.34%, #D2CDF9 99.68%)',
    profileDrawer: 'linear-gradient(180deg, rgba(235, 236, 255, 0.00) 0%, #EBECFF 24.96%), #F7F8FF',
    bookAssistance: 'linear-gradient(351.71deg, #FFF2DF 8.07%, #DADCFF 47.85%, #DADCFF 55.68%, #FFF2DF 94.92%)',
    page: 'linear-gradient(359.13deg, #FFF2DF 0.3%, #DADCFF 50.02%, #FFF2DF 99.82%)',
    gradient2: 'linear-gradient(346deg, #BEF0E4 0%, #FFF2DF 100%)',
    gradient3: 'linear-gradient(346deg, #DADCFF 0%, #FFF2DF 100%)',
    periwinkle: 'linear-gradient(343deg, #FFF2DF -82.35%, #DADCFF 3.68%, #FFF2DF 95.25%)',
  },
  charcoal: {
    70: '#5B5D5F',
    60: '#727476',
    100: '#14181A',
    700: '#535657',
  },
};

export const colors = {
  ...newColor,
  fluentHealth: {
    500: '#495AE4',
  },
  fluentHealthText: {
    100: '#14181A',
    200: '#686A6C',
    250: '#5B60E6',
    300: '#888D8F',
    400: '#A9AEAF',
    500: '#E6E8E8',
    600: '#F7F7F7',
  },
  fluentHealthSecondary: {
    50: 'rgba(73, 90, 228, 0.06)',
    100: '#41399D',
    150: '#3845c5',
    200: '#999CE0',
    300: '#DADCFF',
    400: '#EBECFF',
    450: '#F8F8F8',
    500: '#F7F8FF',
  },
  fluentHealthComplementary: {
    Green: '#2F9673',
    Red: '#EA4747',
    Orange: '#FF824D',
    Yellow: '#FFD99D',
    Salmon4: '#CD6847',
    Salmon3: '#FFC1AD',
    Salmon2: '#FFE0D6',
    Salmon1: '#FFF3F0',
    GrayBorder: '#E7E8E9',
  },
  green: {
    300: '#a5e2bd',
    500: '#289673',
  },
  red: {
    100: '#CF0A0A',
  },
};
